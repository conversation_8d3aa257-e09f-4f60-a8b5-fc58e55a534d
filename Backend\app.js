import express from 'express'
import {nanoid} from 'nanoid'
import connectDB from './src/config/mongodb.config'
const app=express()

connectDB()
app.use(express.json())
app.use(express.urlencoded({extended:true}))

app.get('/',(req,res)=>{
    res.send("Hello World")
})


app.post('/api/create',(req,res)=>{
    const {url}=req.body
    console.log(url)
    res.send(nanoid(7))
})
app.listen(4000,()=>{
    console.log("Server is running on port 4000")
})