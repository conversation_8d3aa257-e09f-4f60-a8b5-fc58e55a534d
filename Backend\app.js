import express from 'express'
import dotenv from 'dotenv'
import connectDB from './src/config/mongodb.config.js'
import cors from 'cors'

dotenv.config()
import shortUrlRouter from './src/routes/shortUrl.route.js'
import { redirectUrl } from './src/controllers/shorturl.controller.js'

const app=express()

connectDB()
app.use(express.json())
app.use(express.urlencoded({extended:true}))
app.use(cors())

app.get('/',(req,res)=>{
    res.send("Hello World")
})

app.use('/api/',shortUrlRouter)


app.get('/:id',redirectUrl)

app.listen(4000,()=>{
    console.log("Server is running on port 4000")
})