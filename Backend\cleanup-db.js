import mongoose from 'mongoose'
import UrlSchema from './src/models/shortUrl.model.js'

// Connect to MongoDB
await mongoose.connect('mongodb://127.0.0.1:27017/urlShort')

console.log('Connected to MongoDB')

// Find all documents
const allUrls = await UrlSchema.find({})
console.log(`Found ${allUrls.length} total entries`)

// Find corrupted entries (where short_url looks like a full URL)
const corruptedEntries = allUrls.filter(entry => 
    entry.short_url.includes('.') || entry.short_url.includes('http')
)

console.log(`Found ${corruptedEntries.length} corrupted entries:`)
corruptedEntries.forEach(entry => {
    console.log(`- ID: ${entry._id}, short_url: "${entry.short_url}", full_url: "${entry.full_url}"`)
})

if (corruptedEntries.length > 0) {
    console.log('\nDeleting corrupted entries...')
    const corruptedIds = corruptedEntries.map(entry => entry._id)
    const result = await UrlSchema.deleteMany({ _id: { $in: corruptedIds } })
    console.log(`Deleted ${result.deletedCount} corrupted entries`)
}

// Show remaining entries
const remainingUrls = await UrlSchema.find({})
console.log(`\nRemaining ${remainingUrls.length} clean entries:`)
remainingUrls.forEach(entry => {
    console.log(`- short_url: "${entry.short_url}" -> full_url: "${entry.full_url}"`)
})

await mongoose.connection.close()
console.log('\nDatabase cleanup completed!')
