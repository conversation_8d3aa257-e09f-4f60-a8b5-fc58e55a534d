import mongoose from 'mongoose'

// Connect to MongoDB
await mongoose.connect('mongodb://127.0.0.1:27017/urlShort')
console.log('Connected to MongoDB')

// List all collections
const collections = await mongoose.connection.db.listCollections().toArray()
console.log('\nAll collections in database:')
collections.forEach(col => console.log(`- ${col.name}`))

// Check each collection for URL-like documents
for (const collection of collections) {
    console.log(`\n=== Collection: ${collection.name} ===`)
    const coll = mongoose.connection.db.collection(collection.name)
    const docs = await coll.find({}).toArray()
    console.log(`Found ${docs.length} documents`)
    
    docs.forEach((doc, index) => {
        console.log(`${index + 1}. ID: ${doc._id}`)
        if (doc.short_url) console.log(`   short_url: "${doc.short_url}"`)
        if (doc.full_url) console.log(`   full_url: "${doc.full_url}"`)
        if (doc.clicks !== undefined) console.log(`   clicks: ${doc.clicks}`)
        console.log()
    })
}

await mongoose.connection.close()
console.log('Database inspection completed!')
