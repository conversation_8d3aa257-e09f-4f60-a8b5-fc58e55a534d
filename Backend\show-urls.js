import mongoose from 'mongoose'
import UrlSchema from './src/models/shortUrl.model.js'

await mongoose.connect('mongodb://127.0.0.1:27017/urlShort')
const urls = await UrlSchema.find({})

console.log('=== CORRECT SHORT URLs TO TEST ===\n')
urls.forEach(url => {
  console.log(`✅ Access: http://localhost:4000/${url.short_url}`)
  console.log(`   Will redirect to: ${url.full_url}`)
  console.log('')
})

console.log('=== WRONG URLs (DON\'T USE THESE) ===\n')
urls.forEach(url => {
  console.log(`❌ DON'T Access: http://localhost:4000/${url.full_url}`)
  console.log(`   This will return "URL not found"`)
  console.log('')
})

await mongoose.connection.close()
