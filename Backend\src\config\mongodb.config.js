import mongoose from 'mongoose'

const connectDB=async ()=>{
    try{
        const conn=await mongoose.connect('mongodb://0.0.0.0:27017/urlShort',{
            useNewUrlParser:true,
            useUnifiedTopology:true,
            useCreateIndex:true
        })
        console.log(`MongoDB Connected: ${conn.connection.host}`)
    }catch(error){
        console.log(`Error: ${error.message}`)
        process.exit(1)
    }
}

export default connectDB