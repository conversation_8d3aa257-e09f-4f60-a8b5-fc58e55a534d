import { CreateshortUrlWithoutUser } from "../services/short_url.service.js"
import UrlSchema from "../models/shortUrl.model.js"
import { getShortUrl } from "../dao/short_url.js"

export const createShortUrl= async(req,res)=>{
    // console.log(req.body)
    const {url } = req.body
    const shortUrl = await CreateshortUrlWithoutUser(url)
    res.send(process.env.APP_URL+ "/"+ shortUrl)

}

export const redirectUrl = async (req,res) => {
    const {id} = req.params
    console.log("Looking for short URL with ID:", id)
    const url = await getShortUrl(id)
    console.log("Result from database:", url)
    if(url){
        res.redirect(url.full_url)
    }else{
        res.status(404).send("URL not found")
    }
}
