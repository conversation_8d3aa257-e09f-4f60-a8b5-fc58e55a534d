import UrlSchema from "../models/shortUrl.model.js" 



export const saveShortUrl=async(longurl,shortUrl,userId)=>{
    const newUrl = new UrlSchema({
        full_url:longurl,
        short_url:shortUrl
    })
    if(userId){
        newUrl.user=userId
    }
    await newUrl.save()
}


export const getShortUrl = async(shortUrl) => {
    const url = await UrlSchema.findOne({short_url:shortUrl})
    return url
}