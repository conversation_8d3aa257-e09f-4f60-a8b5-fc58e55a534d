import { generateNanoId } from "../utils/helper.js"
import { saveShortUrl } from "../dao/short_url.js"

export const CreateshortUrlWithoutUser=async (url) => {
    const shortUrl = generateNanoId(7)
    await saveShortUrl(shortUrl,url)
    return shortUrl
}


export const CreateshortUrlUser=async (url,userId) => {
    const shortUrl = generateNanoId(7)
    await saveShortUrl(url,shortUrl,userId)
    return shortUrl
}
