import React ,{ useState } from 'react'


function URLForm() {
     const [url, setUrl] = useState('')
  const [shortUrl, setShortUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')


  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setShortUrl('')
    
    try {
      const response = await axios.post('http://localhost:4000/api/create', { url })
      setShortUrl(response.data)
    } catch (err) {
      setError('Failed to shorten URL. Please try again.')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

    return (
    <div>
        
        <form  className="space-y-4">
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
              Enter your URL
            </label>
            <input
              type="url"
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
          >
            {loading ? 'Shortening...' : 'Shorten URL'}
          </button>
        </form>

        
   
        {error && (
          <div className="mt-4 text-red-600 text-sm">
            {error}
          </div>
        )}

         
         {shortUrl && (
          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <p className="text-sm text-gray-700 mb-2">Your shortened URL:</p>
            <div className="flex items-center">
              <a 
                href={shortUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline break-all"
              >
                {shortUrl}
              </a>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(shortUrl);
                  alert('URL copied to clipboard!');
                }}
                className="ml-2 p-1 text-gray-500 hover:text-gray-700"
                title="Copy to clipboard"
              >
                📋
              </button>
            </div>
          </div>
        )} 
    </div>
  )
}

export default URLForm